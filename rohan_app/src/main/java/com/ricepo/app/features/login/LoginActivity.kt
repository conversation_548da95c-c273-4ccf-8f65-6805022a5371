package com.ricepo.app.features.login

import android.os.Bundle
import android.text.InputFilter
import android.view.MotionEvent
import android.view.View
import androidx.activity.viewModels
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityLoginBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.base.BaseActivity
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.model.GlobalConfigModel
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.sheet.BaseBottomSheetFragment
import com.ricepo.style.view.CaptchaCodeTextView
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 1/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_LOGIN)
class LoginActivity : BaseActivity() {

  val loginViewModel: LoginViewModel by viewModels()

  private var isChat = false

  private lateinit var binding: ActivityLoginBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    isChat = intent.getBooleanExtra(FeaturePageConst.PARAM_LOGIN_CHAT, false)
    binding = ActivityLoginBinding.inflate(layoutInflater)
    setContentView(binding.root)

    setupListener()
    bindViewModel()

    // 预加载region数据，这样用户点击时就能立即显示
    preloadRegionData()
  }

  override fun onResume() {
    super.onResume()
    // show keyboard with et_phone_number
    KeyboardUtil.showKeyboard(this, binding.etPhoneNumber)
  }

  private fun bindViewModel() {
    loginViewModel.initCountryArea(this).observe(
      this,
      Observer {
        val pair = it
        binding.etPhoneRegion?.setText("+${pair.first.area}")
        binding.etPhoneNumber?.filters = arrayOf(InputFilter.LengthFilter(pair.first.digit))
        binding.etPhoneNumber?.setText("")
      }
    )

    loginViewModel.liveCountdown.observe(
      this,
      Observer {
        binding.tvVcodeResend?.text = it.countdown
        if (it.countdown == ResourcesUtil.getString(com.ricepo.style.R.string.resend)) {
          binding.tvVcodeResend?.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.mainText, binding.root))
        } else {
          binding.tvVcodeResend?.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))
        }
      }
    )

    loginViewModel.liveGetVcode.observe(
      this,
      Observer {
        val option = it
        if (option.message == LoginViewModel.LoginOption.MSG_IS_LOADING) {
          Loading.showLoading(this)
        } else if (option.message?.isNotEmpty() == true) {
          Loading.hideLoading()
          binding.etPhoneNumber?.setText("")
          DialogFacade.showAlert(this, option.message)
          if (option.tooMany) {
            showVcodeView(option)
          }
        } else {
          Loading.hideLoading()
          showVcodeView(option)
        }
      }
    )
  }

  private fun showVcodeView(option: LoginViewModel.LoginOption) {
    // send sms success
    binding.groupLoginPhone?.visibility = View.GONE
    binding.groupLoginVcode?.visibility = View.VISIBLE
    binding.tvVcodePhone?.text = option.phone
    KeyboardUtil.hideKeyboard(this, binding.ivClose)
    binding.ctVcode.showSoftInput()
  }

  private fun setupListener() {

    binding.etPhoneRegion?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        loginViewModel.showAreaBottomSheet().observe(
          this,
          Observer { regionState ->
            when {
              regionState.isLoading -> {
                // 显示加载状态，可以添加loading indicator
                Loading.showLoading(this)
              }
              regionState.data != null -> {
                Loading.hideLoading()
                val (strItems, models) = regionState.data
                if (strItems.isNotEmpty() && models.isNotEmpty()) {
                  showAreaBottomSheet(strItems, models)
                }
              }
              regionState.error != null -> {
                Loading.hideLoading()
                DialogFacade.showAlert(this, regionState.error)
              }
            }
          }
        )
      }
    }

    binding.etFocusPhoneNumber?.setOnTouchListener(object : View.OnTouchListener {
      override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        when (event?.action) {
          MotionEvent.ACTION_DOWN -> {
            KeyboardUtil.toggleKeyboard(binding.etPhoneNumber)
          }
        }
        return true
      }
    })

    binding.etPhoneNumber.doAfterTextChanged { text ->
      loginViewModel.checkLogin(text.toString(), "sms")
    }

    binding.ctVcode.onCompleteListener = object : CaptchaCodeTextView.OnCompleteListener {
      override fun onCompleted(code: String?) {
        loginViewModel.validateCode(code).observe(
          this@LoginActivity,
          Observer {
            val option = it
            if (option.message == LoginViewModel.LoginOption.MSG_IS_LOADING) {
              Loading.showLoading(this@LoginActivity)
            } else if (option.message?.isNotEmpty() == true) {
              Loading.hideLoading()
              binding.ctVcode?.resetCode()
              DialogFacade.showAlert(this@LoginActivity, option.message)
            } else {
              Loading.hideLoading()
              if (isChat) {
                FeaturePageRouter.navigateOrderSupportChat(this@LoginActivity)
                finish()
              } else {
                // success
                backResultEvent(intent)
              }
            }
          }
        )
      }
    }

    binding.tvVcodeResend.touchWithTrigger { textView, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        if (textView.text == ResourcesUtil.getString(com.ricepo.style.R.string.resend)) {
          // resend vscode
          showResendBottomSheet()
        }
      }
    }
  }

  private fun showResendBottomSheet() {
    val datas = listOf(
      ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_sms),
      ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_voice)
    )
    val bottomSheet = BaseBottomSheetFragment.newInstance(datas, datas)
    bottomSheet.onItemTextClickListener = object : BaseBottomSheetFragment.OnItemTextClickListener {
      override fun onItemClick(text: String) {
        binding.ctVcode?.resetCode()
        loginViewModel.resendVcode(text)
      }
    }
    bottomSheet.show(supportFragmentManager, "sheet_resend_vcode")
  }

  // select phone region
  private fun showAreaBottomSheet(datas: List<String>, models: List<GlobalConfigModel>) {
//        val datas = mutableListOf("China +86", "UK +44", "United States +1", "France +33",
//            "Spain +34", "Cancel")
    val bottomSheet = BaseBottomSheetFragment.newInstance(datas, models)
    bottomSheet.onItemClickListener = object : BaseBottomSheetFragment.OnItemClickListener<GlobalConfigModel> {
      override fun onItemClick(text: String, data: GlobalConfigModel?) {
        binding.etPhoneRegion?.setText("+${data?.area}")
        binding.etPhoneNumber?.filters = arrayOf(InputFilter.LengthFilter(data?.digit ?: 10))
        binding.etPhoneNumber?.setText("")
        loginViewModel.setGlobalConfigModel(data)
      }
    }
    bottomSheet.show(supportFragmentManager, "sheet_phone_region")
  }

  /**
   * 预加载region数据
   */
  private fun preloadRegionData() {
    // 静默预加载，不显示loading，不处理结果
    // 这样当用户点击时数据已经准备好了
    loginViewModel.showAreaBottomSheet().observe(this) { regionState ->
      // 静默处理，不需要UI反馈
      // 数据会被缓存在ViewModel中，点击时直接使用
    }
  }

  override fun onDestroy() {
    super.onDestroy()
  }
}
