package com.ricepo.app.features.subscription

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.isVisible
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivitySubscriptionUpdateBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.payment.PaymentUseCase
import com.ricepo.app.features.payment.data.PaymentHandleMode
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.features.subscription.adapter.SubscriptionDetailsAdapter
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.CustomerSubscription
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 15/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_SUBSCRIPTION_UPDATE)
class SubscriptionUpdateActivity : BaseActivity() {

  val subscriptionViewModel: SubscriptionViewModel by viewModels()

  @Inject
  lateinit var mapper: SubscriptionMapper

  @Inject
  lateinit var paymentUseCase: PaymentUseCase

  lateinit var customerSubscription: CustomerSubscription

  private var needUpdate = false

  private lateinit var binding: ActivitySubscriptionUpdateBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivitySubscriptionUpdateBinding.inflate(layoutInflater)
    setContentView(binding.root)
//        setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.subscription_vip))

    customerSubscription = intent.getParcelableExtra(
      FeaturePageConst.PARAM_SUBSCRIPTION_CUSTOMER
    )!!
    if (customerSubscription.plan != null) {
      subscriptionViewModel.onSubscriptionPlan.onNext(customerSubscription.plan!!)
    }

    setupListener()
    bindViewModel()
    // set title with plan title not empty
//        customerSubscription?.plan?.title?.let {
//            setTitleSubText(it.localize())
//        }
  }

  override fun onResume() {
    super.onResume()
    subscriptionViewModel.onResume.onNext(true)
  }

  private fun setupListener() {
    binding.clSubscriptionCancel?.clickWithTrigger {
      DialogFacade.showPrompt(this, com.ricepo.style.R.string.subscription_confirm_cancel) {
        subscriptionViewModel.cancelSubscription(
          this@SubscriptionUpdateActivity, customerSubscription.id
        )
          .subscribe {
            if (!it.message.isNullOrEmpty()) {
              handleFailed(it.message)
            } else {
              DialogFacade.showAlert(
                this@SubscriptionUpdateActivity,
                com.ricepo.style.R.string.subscription_canceled_message
              ) {
                needUpdate = true
                onBackPressed()
              }
            }
          }
      }
    }
  }

  override fun onBackPressed() {
    if (needUpdate) {
      backResultEvent(intent)
    } else {
      super.onBackPressed()
    }
  }

  private fun bindViewModel() {
    subscriptionViewModel.observePaymentByUpdated().subscribe {
      val subscriptionPlan = it.subscriptionPlan
      setPlanView(subscriptionPlan)

      val paymentOwnMethod = it.paymentOwnMethod
      setPaymentView(paymentOwnMethod)
    }
  }

  private fun handleFailed(message: String?) {
    DialogFacade.showAlert(this, message ?: "") {
      // auto check subscription relay
      subscriptionViewModel.onResume.onNext(true)
    }
  }

  private fun setPaymentView(payment: PaymentOwnMethod?) {
    if (payment != null) {
      // payment info
      binding.inCheckoutPayment.ivPaymentIcon?.setImageDrawable(mapper.mapPaymentIcon(this, payment))
      binding.inCheckoutPayment.tvPaymentTitle?.text = mapper.mapPaymentTitle(payment)
    } else {
      // payment none
      binding.inCheckoutPayment.ivPaymentIcon?.setImageResource(com.ricepo.style.R.drawable.ic_payment_none)
      binding.inCheckoutPayment.tvPaymentTitle?.text = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    }
    binding.inCheckoutPayment.tvDividerTop.isVisible = false

    binding.inCheckoutPayment.clPaymentSelect?.clickWithTrigger {
      FeaturePageRouter.navigatePayment(
        this, ShowPaymentCase.onlyStripe,
        PaymentHandleMode.autoUpdateSubscriptionPay
      )
    }
  }

  private fun setPlanView(plan: SubscriptionPlan?) {
    binding.tvSubscriptionTitle?.text = plan?.title?.localize()
    binding.tvSubscriptionSubtitle?.text = mapper.mapUpdateSubtitle(customerSubscription)
//        binding.tvSubscriptionDescription?.text = plan?.description?.localize()?.replace("\\n", "\n")

    val adapter = SubscriptionDetailsAdapter(plan?.descriptions?.localize() ?: listOf())
    binding.rvSubscriptionDetails.adapter = adapter

    binding.clSubscriptionCancel?.isVisible = customerSubscription.cancelAt.isNullOrEmpty()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      needUpdate = true
    }
  }
}
