package com.ricepo.app.features.subscription

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.ActivitySubscriptionBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.payment.PaymentUseCase
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.features.subscription.adapter.SubscriptionDetailsAdapter
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.pattern.payment.PaymentRefer
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.extension.underlineSpan
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.base.model.localize
import com.ricepo.base.tools.AssetUtils
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.network.EnvNetwork.RICEPO_URL_PRIVACY
import com.ricepo.network.EnvNetwork.RICEPO_URL_TERMS
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.trello.rxlifecycle4.android.RxLifecycleAndroid
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by Thomsen on 15/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_SUBSCRIPTION)
class SubscriptionActivity : BaseActivity() {

  val subscriptionViewModel: SubscriptionViewModel by viewModels()

  @Inject
  lateinit var mapper: SubscriptionMapper

  @Inject
  lateinit var paymentUseCase: PaymentUseCase

  private lateinit var binding: ActivitySubscriptionBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivitySubscriptionBinding.inflate(layoutInflater)

    DisplayUtil.logDensity(this)
    Log.i("thom", "sw_70 = ${ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_70dp)}")
    Log.i("thom", "sw_80 = ${ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_80dp)}")

    setContentView(binding.root)
//        setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.subscription_vip))

    subscriptionViewModel.isOnlySubscribe = intent.getBooleanExtra(
      FeaturePageConst.PARAM_SUBSCRIPTION_ONLY_SUBSCRIBE, true
    )
    subscriptionViewModel.isProfileSubscribe = intent.getBooleanExtra(
      FeaturePageConst.PARAM_SUBSCRIPTION_PROFILE_SUBSCRIBE, false
    )
    val subscriptionPlan: SubscriptionPlan? = intent.getParcelableExtra(
      FeaturePageConst.PARAM_SUBSCRIPTION_PLAN
    )
    if (subscriptionPlan != null) {
      subscriptionViewModel.onSubscriptionPlan.onNext(subscriptionPlan)
    }

    subscriptionViewModel.restaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_RESTAURANT)

    bindViewModel()
    // set title with plan title not empty
//        subscriptionPlan?.title?.let {
//            setTitleSubText(it.localize())
//        }
  }

  override fun onResume() {
    super.onResume()
    subscriptionViewModel.onResume.onNext(true)
  }

  private fun bindViewModel() {
    subscriptionViewModel.observePaymentUpdated().subscribe {
      val subscriptionPlan = it.subscriptionPlan
      setPlanView(subscriptionPlan)
      setSubscribeButton(subscriptionPlan)
      setPolicyView()

      val paymentOwnMethod = it.paymentOwnMethod
      setPaymentView(paymentOwnMethod)
      setSubscribeOptButton(paymentOwnMethod)

      // check subscribe success
      val paymentObj = it.paymentObj
      if (paymentObj != null && paymentObj.signed == null) {
        handleSubscribeSuccess()
      }
    }

    subscriptionViewModel.observeVip(this)
      .compose(RxLifecycleAndroid.bindActivity(lifecycle()))
      .subscribe {
        val paymentOwnMethod = it.paymentOwnMethod
        if (paymentOwnMethod == null) {
          FeaturePageRouter.navigatePayment(this, ShowPaymentCase.onlyStripe)
        } else if (it.message?.isNotEmpty() == true) {
          handleFailed(it.message)
        } else if (it.paymentObj != null) {
          val payment = it.paymentObj ?: PaymentObj()
          // payment auth
          paymentUseCase.handlePayment(this, it.paymentOwnMethod, payment, false) { _, _ ->
            // sign will be null handle subscribe success
            handleSubscribeSuccess()
          }
        }
      }
  }

  private fun handleFailed(message: String?) {
    DialogFacade.showAlert(this, message ?: "") {
      // auto check subscription relay
      subscriptionViewModel.onResume.onNext(true)
    }
  }

  private fun handleSubscribeSuccess() {
    // alert success message and navigate to checkout page
    DialogFacade.showAlert(this, com.ricepo.style.R.string.subscription_create_success, canCancel = false) {
      if (subscriptionViewModel.isOnlySubscribe) {
//                backEvent()
        FeaturePageRouter.navigateHomeWithReset(this@SubscriptionActivity)
        finish()
      } else if (subscriptionViewModel.isProfileSubscribe ||
        entrance == FeaturePageConst.PAGE_COUPON
      ) {
        backResultEvent(intent)
      } else {
        subscriptionViewModel.restaurant?.let {
          val restaurantId = it?.id
          if (restaurantId != null) {
            val restaurant = Restaurant(id = restaurantId)
            FeaturePageRouter.navigateCheckoutAfterSubscription(this, restaurant)
          }
        }
      }
    }
  }

  private fun setSubscribeButton(subscriptionPlan: SubscriptionPlan?) {
    RestaurantCartCache.getCountry(subscriptionViewModel.restaurant) {
      val country = it
      binding.tvSubscriptionPrice?.text = mapper.formatPrice(subscriptionPlan?.price ?: 0, country)
    }
  }

  private fun setSubscribeOptButton(opt: PaymentOwnMethod?) {
    if (opt != null) {
      binding.tvSubscriptionPlace?.text = ResourcesUtil.getString(com.ricepo.style.R.string.subscription_join)
    } else {
      binding.tvSubscriptionPlace?.text = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    }
    binding.btnSubscription?.clickWithTrigger {
      subscriptionViewModel.onSubscribeVip.onNext(true)
    }
  }

  private fun setPaymentView(payment: PaymentOwnMethod?) {
    if (payment != null) {
      // payment info
      binding.inCheckoutPayment.ivPaymentIcon?.setImageDrawable(mapper.mapPaymentIcon(this, payment))
      binding.inCheckoutPayment.tvPaymentTitle?.text = mapper.mapPaymentTitle(payment)
    } else {
      // payment none
      binding.inCheckoutPayment.ivPaymentIcon?.setImageResource(com.ricepo.style.R.drawable.ic_payment_none)
      binding.inCheckoutPayment.tvPaymentTitle?.text = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    }
    binding.inCheckoutPayment.tvDividerTop.isVisible = false

    binding.inCheckoutPayment.clPaymentSelect?.clickWithTrigger {
      FeaturePageRouter.navigatePayment(this, ShowPaymentCase.onlyStripe)
    }
  }

  private fun setPolicyView() {
    binding.inCheckoutPolicy.tvPolicyCallSupport?.underlineSpan()
    binding.inCheckoutPolicy.tvPolicyTerms?.underlineSpan()
    binding.inCheckoutPolicy.tvPolicyInfo?.underlineSpan()

    binding.inCheckoutPolicy.tvPolicyCallSupport?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        lifecycleScope.launch {
          val countryCode = withContext(Dispatchers.IO) {
            AddressCache.getCountrySuspend()
          }
          var phoneNumber = AssetUtils.getCountry(countryCode, "phone") as String
          phoneNumber = phoneNumber.replace(" ", "")
          DialogFacade.showPrompt(this@SubscriptionActivity, phoneNumber) {
            IntentUtils.intentPhone(this@SubscriptionActivity, lifecycleScope, phoneNumber)
          }
        }
      }
    }

    binding.inCheckoutPolicy.tvPolicyTerms?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        IntentUtils.intentBrowser(this, RICEPO_URL_TERMS)
      }
    }

    binding.inCheckoutPolicy.tvPolicyInfo?.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        IntentUtils.intentBrowser(this, RICEPO_URL_PRIVACY)
      }
    }
  }

  private fun setPlanView(plan: SubscriptionPlan?) {
    binding.tvSubscriptionTitle?.text = plan?.title?.localize()
    binding.tvSubscriptionSubtitle?.text = plan?.subtitle?.localize()
//        binding.tvSubscriptionDescription?.text = plan?.description?.localize()?.replace("\\n", "\n")

    val adapter = SubscriptionDetailsAdapter(plan?.descriptions ?: listOf())
    binding.rvSubscriptionDetails.adapter = adapter
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_PAYMENT) {
        val payment: PaymentOwnMethod? = data?.getParcelableExtra(
          FeaturePageConst.PARAM_PAGE_PAYMENT_NEW_CARD
        )
        subscriptionViewModel.updateTempPayment(payment)
      }
    }

    // payment authentication completed, get result
    PaymentRefer.onPaymentCardResult(
      this, requestCode, data,
      capture = {
        handleSubscribeSuccess()

        // update temp payment card
        subscriptionViewModel.saveTempPaymentCard()
      },
      completed = {
        handleSubscribeSuccess()
      },
      failed = {
        handleFailed(ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed))
      }
    )

//        val isPaymentResult = StripeClient.instance.onPaymentResult(requestCode, data) { result ->
//            result.fold(
//                onSuccess = { data ->
//                    when(data.intent.status) {
//                        StripeIntent.Status.RequiresPaymentMethod,
//                        StripeIntent.Status.Canceled -> {
//                            handleFailed(ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed))
//                        }
//                        StripeIntent.Status.RequiresCapture -> {
//                            // only applies to payment intents
//                            // handle payment not show animation
//                            handleSubscribeSuccess()
//
//                            // update temp payment card
//                            subscriptionViewModel.saveTempPaymentCard()
//                        }
//                        StripeIntent.Status.RequiresAction,
//                        StripeIntent.Status.RequiresConfirmation -> {
// //                            StripeClient.instance.authenticatePayment(this, data.intent.clientSecret)
//                            StripeClient.instance.confirmPayment(this, data.intent)
//                        }
//                        else -> handleSubscribeSuccess()
//                    }
//
//                },
//                onFailure = { error ->
//                    handleFailed(ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed))
//                }
//            )
//        }
  }
}
