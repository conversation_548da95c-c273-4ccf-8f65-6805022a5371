package com.ricepo.app.features.login

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.address.AddressDao
import com.ricepo.app.features.login.repository.AuthUseCase
import com.ricepo.app.model.UserInformation
import com.ricepo.base.model.Customer
import com.ricepo.base.model.GlobalConfigModel
import com.ricepo.base.tools.AssetUtils
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.ErrorMessage
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import javax.inject.Inject

//
// Created by Thomsen on 2/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class LoginViewModel @Inject constructor(
  val addressDao: AddressDao,
  val useCase: AuthUseCase
) : BaseViewModel() {

  private var globalConfig: GlobalConfigModel = GlobalConfigModel()

  private var listRegions: List<GlobalConfigModel> = listOf()

  // 添加region数据状态管理
  private var isRegionDataLoaded = false
  private var isRegionDataLoading = false

  private var phoneText: String? = null

  private var intervalUnsubscribe: Disposable? = null
  val liveCountdown = MutableLiveData<LoginOption>()

  val liveGetVcode = MutableLiveData<LoginOption>()

  // 添加region数据的LiveData
  val liveRegionData = MutableLiveData<RegionDataState>()

  data class LoginOption(
    val message: String?,
    var phone: String? = null,
    var tooMany: Boolean = false,
    var countdown: String? = null
  ) {
    companion object {
      val MSG_IS_LOADING = "loading"
    }
  }

  // 添加Region数据状态类
  data class RegionDataState(
    val isLoading: Boolean = false,
    val data: Pair<List<String>, List<GlobalConfigModel>>? = null,
    val error: String? = null
  )

  private fun validatePhoneNumber(phone: String): Boolean {
    try {
      val numberProto = PhoneNumberUtil.getInstance()
        .parse("+${globalConfig.area}$phone", globalConfig.area)
      return PhoneNumberUtil.getInstance().isValidNumber(numberProto)
    } catch (e: Exception) {
      return false
    }
  }

  fun checkLogin(phone: String?, type: String) {
    if (phone == null) return
    // the minimum of country digit is 9
    if (phone.count() >= globalConfig.digit) {
      val phoneValidate = validatePhoneNumber(phone)
      if (phoneValidate) {
        liveGetVcode.postValue(LoginOption(LoginOption.MSG_IS_LOADING))
        this.phoneText = phone
        val standPhone = "+${globalConfig.area}$phone"
        getVcode(standPhone, type)
      } else {
        if (phone.count() == globalConfig.digit) {
          liveGetVcode.postValue(LoginOption(ResourcesUtil.getString(com.ricepo.style.R.string.error_invalid_phone_number)))
        }
      }
    }
  }

  /**
   * [type] is sms or call
   */
  fun getVcode(phone: String?, type: String) {
    val lPhone = phone ?: return
    useCase.getVcode(
      object : DisposableSingleObserver<Any>() {
        override fun onSuccess(t: Any) {
          liveGetVcode.postValue(LoginOption(null, phone = "+${globalConfig.area} $phoneText"))
          countdownVcode(30)
        }

        override fun onError(e: Throwable) {
          e?.printStackTrace()
          if (e is NetworkError) {
            handleNetworkError(e, liveGetVcode, lPhone)
          } else {
            liveGetVcode.postValue(LoginOption(ErrorMessage.NETWORK_ERROR))
//            MonitorFacade.captureException(
//              Exception(ErrorMessage.NETWORK_ERROR),
//              mapOf("phone" to phone, "type" to type)
//            )
          }
        }
      },
      AuthUseCase.Params(lPhone, type, "")
    )
  }

  private fun login(phone: String?, vcode: String, liveData: MutableLiveData<LoginOption>) {
    val lPhone = phone ?: return
    useCase.login(
      object : DisposableSingleObserver<UserInformation>() {
        override fun onSuccess(t: UserInformation) {
          // save token
          saveToken(t, liveData)
        }

        override fun onError(e: Throwable) {
          e?.printStackTrace()
          if (e is NetworkError) {
            handleNetworkError(e, liveData, lPhone)
          } else {
            liveData.postValue(LoginOption(ErrorMessage.NETWORK_ERROR))
//            MonitorFacade.captureException(
//              Exception(ErrorMessage.NETWORK_ERROR),
//              mapOf("phone" to phone, "vcode" to vcode)
//            )
          }
        }
      },
      AuthUseCase.Params(lPhone, "", vcode)
    )
  }

  private fun saveToken(
    t: UserInformation?,
    liveData: MutableLiveData<LoginOption>
  ) {
    viewModelScope.launch {
//            if (t is Map<*, *> && t.get("token") != null) {
//                val token = t.get("token") as String
//                KeyCacheFacade.saveToken(token)
//                val customerId = t.get("_id") as String
//                getCustomer(customerId, liveData)
//            }
      if (t != null) {
        CustomerCache.saveToken(t.token)
        getCustomer(t.id, liveData)
      }
    }
  }

  private fun getCustomer(customerId: String, liveData: MutableLiveData<LoginOption>) {
    if (customerId != null) {
      useCase.getCustomer(
        object : DisposableSingleObserver<Customer>() {
          override fun onSuccess(t: Customer) {
            viewModelScope.launch {
              withContext(Dispatchers.IO) {
                // save customer info
                CustomerCache.saveCustomer(t)
              }

              // login success
              liveData.postValue(LoginOption(null))
            }
          }

          override fun onError(e: Throwable) {
            liveData.postValue(LoginOption(ErrorMessage.NETWORK_ERROR))
//            MonitorFacade.captureException(
//              Exception(ErrorMessage.NETWORK_ERROR),
//              mapOf("customerId" to customerId)
//            )
          }
        },
        customerId
      )
    }
  }

  private fun handleNetworkError(
    t: NetworkError,
    liveData: MutableLiveData<LoginOption>,
    phone: String
  ) {
    if (t.code == ErrorCode.TWO_MANY_REQUESTS && t.details != null) {
      val map = t.details
      val remaining = map?.get("remaining")
      var remain = 0
      if (remaining != null) {
        try {
          remain = (remaining as Double).toInt()
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
      countdownVcode(remain)
      liveData.postValue(
        LoginOption(
          ResourcesUtil.getString(com.ricepo.style.R.string.error_too_many_requests, remain),
          tooMany = true, phone = "+${globalConfig.area} $phoneText"
        )
      )
    } else if (t.code == ErrorCode.AUTH_FAILED) {
      liveData.postValue(LoginOption(ResourcesUtil.getString(com.ricepo.style.R.string.error_auth_failed)))
    } else {
      liveData.postValue(LoginOption(t.message ?: ErrorMessage.NETWORK_ERROR))
      if (t.message == null) {
//        MonitorFacade.captureException(
//          Exception(ErrorMessage.NETWORK_ERROR),
//          mapOf("phone" to phone)
//        )
      }
    }
  }

  private fun countdownVcode(remain: Int) {
    if (intervalUnsubscribe != null) {
      intervalUnsubscribe?.dispose()
    }
    intervalUnsubscribe = Observable.interval(0, 1, TimeUnit.SECONDS)
      .map { it + 1 }
      .map { remain - it }
      .distinct()
      .subscribe {
        val resend = ResourcesUtil.getString(com.ricepo.style.R.string.resend)
        val text = if (it > 0) "${ResourcesUtil.getString(com.ricepo.style.R.string.wait_seconds, it)} $resend" else {
          intervalUnsubscribe?.dispose()
          resend
        }
        liveCountdown.postValue(LoginOption(null, countdown = text))
      }
  }

  fun initCountryArea(lifecycleOwner: LifecycleOwner):
    LiveData<Pair<GlobalConfigModel, List<GlobalConfigModel>>> {
    val liveData = MutableLiveData<Pair<GlobalConfigModel, List<GlobalConfigModel>>>()
    viewModelScope.launch {
      val countryMap = useCase.getCountry()
      liveCountry(lifecycleOwner, liveData, countryMap)

      // 预加载region数据
      preloadRegionData(countryMap)
    }
    return liveData
  }

  private fun liveCountry(
    lifecycleOwner: LifecycleOwner,
    liveData: MutableLiveData<Pair<GlobalConfigModel, List<GlobalConfigModel>>>,
    cm: Map<String, GlobalConfigModel>?
  ) {
    addressDao.liveAddressLatest().observe(
      lifecycleOwner,
      Observer {
        val address = it
        val countryMap = cm ?: AssetUtils.getCountryModel()
        val country = address?.country ?: "ES"

        // default ES if not in regions
        var dic = countryMap?.get(country)
        if (dic == null) {
          dic = countryMap?.get("ES")
        }

        dic?.let { config ->
          globalConfig = config
        }

        // 确保listRegions总是有数据
        if (countryMap?.values != null) {
          listRegions = countryMap.values.toList().fold(mutableListOf()) { result, item ->
            result.add(item)
            result
          }
          isRegionDataLoaded = true
        } else {
          // 如果countryMap为空，尝试从AssetUtils获取备用数据
          val backupCountryMap = AssetUtils.getCountryModel()
          if (backupCountryMap?.values != null) {
            listRegions = backupCountryMap.values.toList()
            isRegionDataLoaded = true
          }
        }

        liveData.apply {
          value = Pair(globalConfig, listRegions)
        }
      }
      )
  }

  fun showAreaBottomSheet(): LiveData<RegionDataState> {
    // 如果数据已经加载，直接返回
    if (isRegionDataLoaded && listRegions.isNotEmpty()) {
      val sortedItems = listRegions.sortedBy { it.order }
      val strItems = sortedItems
        .fold(mutableListOf<String>()) { result, it ->
          result.add("${it.name} +${it.area}")
          result
        }

      liveRegionData.value = RegionDataState(
        isLoading = false,
        data = Pair(strItems, sortedItems),
        error = null
      )
      return liveRegionData
    }

    // 如果正在加载，返回加载状态
    if (isRegionDataLoading) {
      liveRegionData.value = RegionDataState(isLoading = true)
      return liveRegionData
    }

    // 如果没有数据且没有在加载，开始加载
    loadRegionData()
    return liveRegionData
  }

  /**
   * 预加载region数据
   */
  private fun preloadRegionData(countryMap: Map<String, GlobalConfigModel>?) {
    if (isRegionDataLoaded || isRegionDataLoading) return

    isRegionDataLoading = true
    liveRegionData.value = RegionDataState(isLoading = true)

    viewModelScope.launch {
      try {
        val regions = if (countryMap?.values != null) {
          countryMap.values.toList()
        } else {
          // 使用AssetUtils获取备用数据
          val backupCountryMap = AssetUtils.getCountryModel()
          backupCountryMap?.values?.toList() ?: listOf()
        }

        if (regions.isNotEmpty()) {
          listRegions = regions
          isRegionDataLoaded = true

          val sortedItems = regions.sortedBy { it.order }
          val strItems = sortedItems
            .fold(mutableListOf<String>()) { result, it ->
              result.add("${it.name} +${it.area}")
              result
            }

          liveRegionData.value = RegionDataState(
            isLoading = false,
            data = Pair(strItems, sortedItems),
            error = null
          )
        } else {
          liveRegionData.value = RegionDataState(
            isLoading = false,
            data = null,
            error = "No region data available"
          )
        }
      } catch (e: Exception) {
        liveRegionData.value = RegionDataState(
          isLoading = false,
          data = null,
          error = e.message ?: "Failed to load region data"
        )
      } finally {
        isRegionDataLoading = false
      }
    }
  }

  /**
   * 加载region数据（当点击时没有数据时调用）
   */
  private fun loadRegionData() {
    if (isRegionDataLoading) return

    isRegionDataLoading = true
    liveRegionData.value = RegionDataState(isLoading = true)

    viewModelScope.launch {
      try {
        val countryMap = useCase.getCountry()
        preloadRegionData(countryMap)
      } catch (e: Exception) {
        // 如果网络请求失败，尝试使用本地数据
        val backupCountryMap = AssetUtils.getCountryModel()
        if (backupCountryMap?.values != null) {
          preloadRegionData(backupCountryMap)
        } else {
          liveRegionData.value = RegionDataState(
            isLoading = false,
            data = null,
            error = e.message ?: "Failed to load region data"
          )
          isRegionDataLoading = false
        }
      }
    }
  }

  fun setGlobalConfigModel(data: GlobalConfigModel?) {
    globalConfig = data ?: GlobalConfigModel()
  }

  fun validateCode(code: String?): LiveData<LoginOption> {
    val liveData = MutableLiveData<LoginOption>()
    // the length of captcha code is four
    if (code != null && code.length == 4) {
      liveData.postValue(LoginOption(LoginOption.MSG_IS_LOADING))
      val standPhone = "+${globalConfig.area}$phoneText"
      login(standPhone, code, liveData)
    }
    return liveData
  }

  fun resendVcode(text: String) {
    if (text == ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_sms)) {
      checkLogin(phoneText, "sms")
    }
    if (text == ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_voice)) {
      checkLogin(phoneText, "call")
    }
  }

  override fun onCleared() {
    super.onCleared()
    intervalUnsubscribe?.dispose()
  }
}
