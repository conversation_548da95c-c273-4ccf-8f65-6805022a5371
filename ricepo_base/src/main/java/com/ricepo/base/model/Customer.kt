package com.ricepo.base.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.base.remoteconfig.ReferralConfig
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 17/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Parcelize
data class Customer(
  @SerializedName("_id")
  val id: String,
  val createdAt: String? = null,
  val orderCount: Int? = null,
  val saving: Int? = null,
  val phone: String? = null,
  val refer: CustomerRefer? = null,
  val recommendation: CustomRecommend? = null,
  val subscription: CustomerSubscription? = null,
  val plan: SubscriptionPlan? = null,

  val region: RegionModel? = null,
  val language: String? = null,
  val lastOrder: CustomerOrder? = null,
  val infoCount: InfoCount? = null,
  val point: RicepoPoint? = null,
  val email: UserEmail? = null
) : Parcelable {
  val days: Long
    get() {
      return (BaseMapper().daysOnNow(createdAt) ?: 1)
    }

  fun isReferEnable(
    referralConfig: ReferralConfig
  ): Boolean {
//    referralConfig.enable_area.forEach {
//      if (phone?.startsWith(it) == true) {
//        return true
//      }
//    }
    return true
  }
}

@Parcelize
data class UserEmail(val address: String?) : Parcelable

@Parcelize
data class RicepoPoint(
  val type: String? = null,
  val balance: PointBalance? = null
) : Parcelable

@Parcelize
data class PointBalance(
  val total: Int? = null,
  val available: Int? = null,
  val pending: Int? = null,
  val purchase: Int? = null,
  val description: InternationalizationContent? = null,
  val price: Int? = null
) : Parcelable

@Parcelize
data class InfoCount(
  val coupons: Int? = null,
  // restaurant reward
  val reward: Int? = null,
  // ricepo reward
  val points: Int? = null
) : Parcelable

@Parcelize
data class CustomerRefer(
  val code: String? = null,
  // shared reward, if the server does not return, default to 10000
  var reward: Int? = 1000,
  var multiplier: Int? = 2,
  var banner: ReferBanner? = null,
  var share: ReferShare? = null,
  var point: Int? = null
) : Parcelable

@Parcelize
data class ReferBanner(
  val title: InternationalizationContent? = null,
  val descrpition: InternationalizationContent? = null,
) : Parcelable

@Parcelize
data class ReferShare(
  val url: String? = null,
  val title: InternationalizationContent? = null,
  val description: InternationalizationContent? = null,
  val message: InternationalizationContent? = null,
  val copy: InternationalizationContent? = null,
) : Parcelable

/**
 * Custom Recommend section
 *
 * recent: Recently ordered
 * cross: People like you
 * popular: Most popular
 * featured: Platform recommendation
 */
@Parcelize
data class CustomRecommend(
  var recent: List<Food>? = null,
  var cross: List<Food>? = null,
  var popular: List<Food>? = null,
  var featured: List<Food>? = null
) : Parcelable {

  /**
   * Too many judgments, values, calculations, etc. in an expression will report the following error:
   * `The compiler is unable to type-check this expression in reasonable time; try breaking up the express`
   * Breaking up the expression into distinct sub-expressions
   */
  fun flatten(): List<Food> {
    val recent = (this.recent ?: listOf())
    val cross = (this.cross ?: listOf())
    val popular = (this.popular ?: listOf())
    val featured = (this.featured ?: listOf())
    return recent + cross + popular + featured
  }
}

@Parcelize
data class CustomerSubscription(
  val id: String?,
  val plan: SubscriptionPlan?,
  val paymentMethod: SubscriptionPaymentMethod?,
  val status: String?,
  val currentPeriodStartAt: String?,
  val currentPeriodEndAt: String?,
  val cancelAt: String?,
  val canceledAt: String?
) : Parcelable

@Parcelize
data class SubscriptionPaymentMethod(
  val id: String?,
  val brand: String?,
  val last4: String?,
  // / Determine payment method by this bool,
  // / if true, payment method is applyPay, otherwise, payment method is credit
  val applePay: Boolean?
) : Parcelable

// / Subscription plan struct
@Parcelize
data class SubscriptionPlan(
  @SerializedName("_id")
  val id: String?,
  val type: String?,
  val name: InternationalizationContent?,
  val price: Int?,
  val title: InternationalizationContent?,
  val subtitle: InternationalizationContent?,
  val description: InternationalizationContent?,
  val button: InternationalizationContent?,
  val descriptions: List<SubscriptionPlanDesc>?
) : Parcelable

@Parcelize
data class SubscriptionPlanDesc(
  val title: InternationalizationContent? = null,
  val subTitle: InternationalizationContent? = null
): Parcelable


@Parcelize
data class CustomerOrder(
  @SerializedName("_id")
  val id: String?,
  val createdAt: String?
) : Parcelable
